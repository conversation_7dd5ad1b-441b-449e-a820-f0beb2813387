/**
 * @file http_server.h
 * @brief HTTP服务器框架主类 - 基于事件驱动的高性能HTTP服务器
 * <AUTHOR>
 * @date 2025/7/11
 * @version 1.0
 *
 * 功能特性:
 * - 基于EventLoop的事件驱动架构
 * - 支持Keep-Alive长连接
 * - 线程池异步请求处理
 * - 路由管理和中间件支持
 * - 配置热更新
 * - 完善的错误处理和日志记录
 * - 性能监控和统计
 * - SSL/TLS支持（可选）
 *
 * 架构设计:
 * - 主线程：事件循环，接受连接
 * - 工作线程：处理HTTP请求
 * - IO线程：网络数据传输
 * - 统计线程：性能监控和统计
 *
 * 与现有模块集成:
 * - 使用网络模块的EventLoop、Socket、Channel
 * - 集成ThreadPool进行并发处理
 * - 使用ConfigManager进行配置管理
 * - 使用Logger进行日志记录
 * - 支持与TaskScheduler的任务调度集成
 *
 * 使用示例:
 * @code
 * HttpServerConfig config;
 * config.host = "0.0.0.0";
 * config.port = 8080;
 * config.worker_threads = 4;
 * 
 * HttpServer server(config);
 * 
 * // 添加中间件
 * server.use(std::make_shared<CorsMiddleware>());
 * server.use(std::make_shared<LoggingMiddleware>());
 * 
 * // 添加路由
 * server.get("/api/users", userListHandler);
 * server.post("/api/users", createUserHandler);
 * 
 * // 启动服务器
 * server.start();
 * @endcode
 */

#ifndef HTTP_SERVER_H
#define HTTP_SERVER_H

#include <string>
#include <memory>
#include <atomic>
#include <mutex>
#include <thread>
#include <unordered_map>
#include <functional>
#include <fstream>
#include <sstream>
#include <iomanip>
#include <algorithm>
#include <set>
#include <vector>
#include <ctime>
#include <sys/stat.h>
#include <dirent.h>
#include <unistd.h>
#include <cstdint>

#include <climits>

#include "common/http/http_request.h"
#include "common/http/http_response.h"
#include "common/http/http_handler.h"
#include "common/http/http_router.h"
#include "common/http/http_session.h"
#include "common/http/http_middleware.h"
#include "common/network/event_loop.h"
#include "common/network/socket.h"
#include "common/network/channel.h"
#include "common/network/inet_address.h"
#include "common/thread_pool/thread_pool.h"
#include "common/logger/logger.h"

namespace common {
    namespace http {

        /**
         * @brief HTTP服务器配置
         */
        struct HttpServerConfig {
            // 网络配置
            std::string host = "0.0.0.0";              ///< 监听地址
            int port = 8080;                           ///< 监听端口
            int backlog = 1024;                        ///< 监听队列长度
            bool reuse_addr = true;                    ///< 启用地址重用
            bool reuse_port = false;                   ///< 启用端口重用

            // HTTP协议配置
            size_t max_request_size = 1024 * 1024;     ///< 最大请求大小（1MB）
            size_t max_header_size = 8192;             ///< 最大头部大小（8KB）
            int request_timeout_ms = 30000;            ///< 请求超时时间（30秒）
            int keep_alive_timeout_ms = 60000;         ///< Keep-Alive超时时间（60秒）
            int max_keep_alive_requests = 100;         ///< 单连接最大请求数

            // 线程池配置
            int worker_threads = 0;                    ///< 工作线程数（0=自动检测）
            int io_threads = 1;                        ///< IO线程数
            bool enable_thread_pool = true;            ///< 启用线程池

            // 性能优化配置
            bool enable_tcp_nodelay = true;            ///< 禁用Nagle算法
            bool enable_so_reuseaddr = true;           ///< SO_REUSEADDR选项
            int send_buffer_size = 65536;              ///< 发送缓冲区大小（64KB）
            int recv_buffer_size = 65536;              ///< 接收缓冲区大小（64KB）

            // 安全配置
            bool enable_cors = true;                   ///< 启用CORS
            std::string cors_origin = "*";             ///< CORS允许的源

            // 日志和监控配置
            bool enable_access_log = true;             ///< 启用访问日志
            bool enable_error_log = true;              ///< 启用错误日志
            bool enable_performance_monitoring = true; ///< 启用性能监控
            int stats_interval_ms = 5000;              ///< 统计间隔（5秒）

            // 静态文件服务配置
            bool enable_directory_listing = false;     ///< 启用目录浏览
            size_t max_static_file_size = 100 * 1024 * 1024; ///< 最大静态文件大小（100MB）
            bool enable_file_cache = true;             ///< 启用文件缓存
            int static_file_cache_time = 3600;         ///< 静态文件缓存时间（秒）
            size_t thread_pool_size = 4;               ///< 线程池大小

            /**
             * @brief 验证配置
             * @return 配置有效返回true
             */
            bool validate() const {
                return port > 0 && port <= 65535 &&
                       !host.empty() &&
                       max_request_size > 0 &&
                       max_header_size > 0 &&
                       request_timeout_ms > 0 &&
                       keep_alive_timeout_ms > 0 &&
                       worker_threads >= 0 &&
                       io_threads > 0;
            }
        };

        /**
         * @brief HTTP服务器统计信息
         */
        struct HttpServerStats {
            std::atomic<uint64_t> total_connections{0};     ///< 总连接数
            std::atomic<uint64_t> active_connections{0};    ///< 活跃连接数
            std::atomic<uint64_t> total_requests{0};        ///< 总请求数
            std::atomic<uint64_t> total_responses{0};       ///< 总响应数
            std::atomic<uint64_t> error_count{0};           ///< 错误次数
            std::atomic<uint64_t> timeout_count{0};         ///< 超时次数
            std::chrono::steady_clock::time_point start_time; ///< 启动时间

            /**
             * @brief 构造函数
             */
            HttpServerStats() : start_time(std::chrono::steady_clock::now()) {}

            /**
             * @brief 获取运行时间（秒）
             */
            long getUptimeSeconds() const {
                auto now = std::chrono::steady_clock::now();
                return std::chrono::duration_cast<std::chrono::seconds>(now - start_time).count();
            }

            /**
             * @brief 转换为JSON格式
             */
            nlohmann::json toJson() const {
                return {
                    {"total_connections", total_connections.load()},
                    {"active_connections", active_connections.load()},
                    {"total_requests", total_requests.load()},
                    {"total_responses", total_responses.load()},
                    {"error_count", error_count.load()},
                    {"timeout_count", timeout_count.load()},
                    {"uptime_seconds", getUptimeSeconds()}
                };
            }
        };

        /**
         * @brief HTTP服务器主类
         * @details 基于事件驱动的高性能HTTP服务器实现
         * 
         * 核心职责:
         * - 网络连接管理：监听、接受、管理客户端连接
         * - 请求处理：HTTP请求解析、路由分发、响应生成
         * - 中间件管理：中间件注册、执行、生命周期管理
         * - 性能优化：线程池、连接池、缓存等性能优化
         * - 监控统计：连接统计、性能监控、健康检查
         * - 配置管理：配置加载、热更新、验证
         */
        class HttpServer {
        public:
            /**
             * @brief 构造函数
             * @param config 服务器配置
             */
            explicit HttpServer(const HttpServerConfig& config = HttpServerConfig{});

            /**
             * @brief 构造函数（简化版本）
             * @param host 监听地址
             * @param port 监听端口
             */
            HttpServer(const std::string& host, int port);

            /**
             * @brief 析构函数
             */
            ~HttpServer();

            // 禁用拷贝构造和赋值
            HttpServer(const HttpServer&) = delete;
            HttpServer& operator=(const HttpServer&) = delete;

            // ==================== 服务器控制方法 ====================

            /**
             * @brief 启动服务器
             * @return 启动成功返回true
             */
            bool start();

            /**
             * @brief 停止服务器
             */
            void stop();

            /**
             * @brief 等待服务器停止
             */
            void waitForStop();

            /**
             * @brief 检查服务器是否正在运行
             */
            bool isRunning() const { return running_.load(); }

            // ==================== 路由管理方法 ====================

            /**
             * @brief 添加路由
             * @param method HTTP方法
             * @param path 路径
             * @param handler 处理器
             */
            void route(const std::string& method, const std::string& path, RequestHandler handler);

            /**
             * @brief GET路由
             */
            void get(const std::string& path, RequestHandler handler);

            /**
             * @brief POST路由
             */
            void post(const std::string& path, RequestHandler handler);

            /**
             * @brief PUT路由
             */
            void put(const std::string& path, RequestHandler handler);

            /**
             * @brief DELETE路由
             */
            void del(const std::string& path, RequestHandler handler);

            /**
             * @brief PATCH路由
             */
            void patch(const std::string& path, RequestHandler handler);

            /**
             * @brief OPTIONS路由
             */
            void options(const std::string& path, RequestHandler handler);

            // ==================== 中间件管理方法 ====================

            /**
             * @brief 添加全局中间件
             * @param middleware 中间件
             */
            void use(MiddlewareHandler middleware);

            /**
             * @brief 添加路径特定中间件
             * @param path 路径
             * @param middleware 中间件
             */
            void use(const std::string& path, MiddlewareHandler middleware);

            // ==================== 静态文件服务 ====================

            /**
             * @brief 设置静态文件服务
             * @param path URL路径
             * @param directory 本地目录
             */
            void serveStatic(const std::string& path, const std::string& directory);

            // ==================== 错误处理 ====================

            /**
             * @brief 设置错误处理器
             * @param handler 错误处理器
             */
            void setErrorHandler(ErrorHandler handler);

            /**
             * @brief 设置404处理器
             * @param handler 404处理器
             */
            void setNotFoundHandler(RequestHandler handler);

            // ==================== 配置管理 ====================

            /**
             * @brief 更新配置
             * @param config 新配置
             */
            void updateConfig(const HttpServerConfig& config);

            /**
             * @brief 从配置管理器加载配置
             */
            void loadConfigFromManager();

            /**
             * @brief 获取当前配置
             */
            const HttpServerConfig& getConfig() const { return config_; }

            // ==================== 统计和监控 ====================

            /**
             * @brief 获取服务器统计信息
             */
            const HttpServerStats& getStats() const { return stats_; }

            /**
             * @brief 获取活跃会话数
             */
            size_t getActiveSessionCount() const {
                std::lock_guard<std::mutex> lock(sessions_mutex_);
                return sessions_.size();
            }

            std::vector<std::string> getAllRoutes()
            {
                return router_->getAllRoutes();
            }

        private:
            // ==================== 静态文件服务数据结构 ====================

            /**
             * @brief 文件信息结构
             */
            struct FileInfo {
                bool valid = false;                 ///< 信息是否有效
                size_t size = 0;                    ///< 文件大小
                time_t last_modified = 0;           ///< 最后修改时间
                std::string etag;                   ///< ETag值
                bool is_directory = false;          ///< 是否为目录
            };

            /**
             * @brief HTTP范围请求信息
             */
            struct RangeInfo {
                bool valid = false;                 ///< 范围是否有效
                size_t start = 0;                   ///< 起始位置
                size_t end = 0;                     ///< 结束位置
            };

            /**
             * @brief 目录项信息
             */
            struct DirectoryEntry {
                std::string name;                   ///< 文件/目录名
                bool is_directory = false;          ///< 是否为目录
                size_t size = 0;                    ///< 文件大小
                time_t last_modified = 0;           ///< 最后修改时间
            };

            // ==================== 私有成员变量 ====================
            HttpServerConfig config_;                           ///< 服务器配置
            std::atomic<bool> running_;                         ///< 是否正在运行
            std::atomic<bool> stopping_;                       ///< 是否正在停止

            // 网络组件
            std::unique_ptr<common::network::EventLoop> event_loop_;        ///< 事件循环
            std::unique_ptr<common::network::Socket> listen_socket_;        ///< 监听套接字
            std::unique_ptr<common::network::Channel> listen_channel_;      ///< 监听通道
            std::unique_ptr<common::network::InetAddress> listen_address_;  ///< 监听地址

            // 处理组件
            std::unique_ptr<HttpRouter> router_;                ///< 路由器
            std::unique_ptr<MiddlewareManager> middleware_manager_; ///< 中间件管理器
            std::shared_ptr<common::thread_pool::ThreadPool> thread_pool_; ///< 线程池

            // 会话管理
            std::unordered_map<int, std::shared_ptr<HttpSession>> sessions_; ///< 活跃会话
            mutable std::mutex sessions_mutex_;                 ///< 会话锁

            // 处理器
            ErrorHandler error_handler_;                        ///< 错误处理器
            RequestHandler not_found_handler_;                  ///< 404处理器

            // 统计信息
            HttpServerStats stats_;                             ///< 统计信息
            std::thread stats_thread_;                          ///< 统计线程

            // ==================== 私有方法 ====================

            /**
             * @brief 初始化服务器
             * @return 初始化成功返回true
             */
            bool initialize();

            /**
             * @brief 处理新连接
             */
            void handleNewConnection();

            /**
             * @brief 处理HTTP请求
             * @param session 会话
             * @param request 请求
             * @param response 响应
             */
            void handleHttpRequest(std::shared_ptr<HttpSession> session, 
                                 const HttpRequest& request, HttpResponse& response);

            /**
             * @brief 处理请求
             * @param request 请求
             * @param response 响应
             */
            void processRequest(const HttpRequest& request, HttpResponse& response);

            /**
             * @brief 清理会话
             * @param session_id 会话ID
             */
            void cleanupSession(int session_id);

            /**
             * @brief 统计线程函数
             */
            void statsThreadFunc();

            /**
             * @brief 设置默认处理器
             */
            void setupDefaultHandlers();

            // ==================== 静态文件服务私有方法 ====================

            /**
             * @brief 处理静态文件请求（核心方法）
             * @param request HTTP请求
             * @param response HTTP响应
             * @param base_directory 基础目录
             * @param url_path URL路径前缀
             * @param head_only 是否只返回头部（HEAD请求）
             */
            void handleStaticFileRequest(const HttpRequest& request, HttpResponse& response,
                                        const std::string& base_directory, const std::string& url_path, bool head_only = false);

            /**
             * @brief 从请求路径提取文件路径
             * @param request_path 请求路径
             * @param url_path URL前缀
             * @return 文件路径
             */
            std::string extractFilePath(const std::string& request_path, const std::string& url_path);

            /**
             * @brief 检查路径安全性（防止目录遍历攻击）
             * @param full_path 完整路径
             * @param base_directory 基础目录
             * @return 路径是否安全
             */
            bool isPathSafe(const std::string& full_path, const std::string& base_directory);

            /**
             * @brief 标准化目录路径
             * @param path 目录路径
             * @return 标准化后的路径
             */
            std::string normalizeDirectoryPath(const std::string& path);

            /**
             * @brief 标准化文件路径
             * @param path 文件路径
             * @return 标准化后的路径
             */
            std::string normalizeFilePath(const std::string& path);

            /**
             * @brief 获取规范化路径
             * @param path 路径
             * @return 规范化路径
             */
            std::string getCanonicalPath(const std::string& path);

            /**
             * @brief 检查文件是否存在
             * @param file_path 文件路径
             * @return 文件是否存在
             */
            bool fileExists(const std::string& file_path);

            /**
             * @brief 检查目录是否存在
             * @param dir_path 目录路径
             * @return 目录是否存在
             */
            bool directoryExists(const std::string& dir_path);

            /**
             * @brief 检查文件读取权限
             * @param file_path 文件路径
             * @return 是否有读取权限
             */
            bool hasFileReadPermission(const std::string& file_path);

            /**
             * @brief 检查目录读取权限
             * @param dir_path 目录路径
             * @return 是否有读取权限
             */
            bool hasDirectoryReadPermission(const std::string& dir_path);

            // ==================== 文件信息和内容处理方法 ====================

            /**
             * @brief 获取文件信息
             * @param file_path 文件路径
             * @return 文件信息
             */
            FileInfo getFileInfo(const std::string& file_path);

            /**
             * @brief 检查路径是否为目录
             * @param path 路径
             * @return 是否为目录
             */
            bool isDirectory(const std::string& path);

            /**
             * @brief 查找索引文件
             * @param dir_path 目录路径
             * @return 索引文件路径（如果找到）
             */
            std::string findIndexFile(const std::string& dir_path);

            /**
             * @brief 设置静态文件响应头
             * @param response HTTP响应
             * @param file_info 文件信息
             * @param file_path 文件路径
             */
            void setStaticFileHeaders(HttpResponse& response, const FileInfo& file_info, const std::string& file_path);

            /**
             * @brief 获取MIME类型
             * @param file_path 文件路径
             * @return MIME类型
             */
            std::string getMimeType(const std::string& file_path);

            /**
             * @brief 获取文件扩展名
             * @param file_path 文件路径
             * @return 文件扩展名
             */
            std::string getFileExtension(const std::string& file_path);

            /**
             * @brief 判断文件是否可缓存
             * @param file_path 文件路径
             * @return 是否可缓存
             */
            bool isCacheableFile(const std::string& file_path);

            /**
             * @brief 判断文件是否为可执行文件
             * @param file_path 文件路径
             * @return 是否为可执行文件
             */
            bool isExecutableFile(const std::string& file_path);

            // ==================== 条件请求和缓存处理方法 ====================

            /**
             * @brief 处理条件请求
             * @param request HTTP请求
             * @param response HTTP响应
             * @param file_info 文件信息
             * @return 是否返回304 Not Modified
             */
            bool handleConditionalRequest(const HttpRequest& request, HttpResponse& response, const FileInfo& file_info);

            /**
             * @brief 格式化HTTP日期
             * @param timestamp 时间戳
             * @return HTTP日期字符串
             */
            std::string formatHttpDate(time_t timestamp);

            /**
             * @brief 解析HTTP日期
             * @param date_str 日期字符串
             * @return 时间戳
             */
            time_t parseHttpDate(const std::string& date_str);

            // ==================== 文件内容读取方法 ====================

            /**
             * @brief 处理普通文件请求
             * @param response HTTP响应
             * @param file_info 文件信息
             * @param file_path 文件路径
             * @param head_only 是否只返回头部
             */
            void handleNormalFileRequest(HttpResponse& response, const FileInfo& file_info,
                                       const std::string& file_path, bool head_only = false);

            /**
             * @brief 读取文件内容
             * @param file_path 文件路径
             * @return 文件内容
             */
            std::string readFileContent(const std::string& file_path);

            /**
             * @brief 读取文件范围
             * @param file_path 文件路径
             * @param start 起始位置
             * @param length 读取长度
             * @return 文件内容
             */
            std::string readFileRange(const std::string& file_path, size_t start, size_t length);

            // ==================== 范围请求处理方法 ====================

            /**
             * @brief 处理范围请求
             * @param request HTTP请求
             * @param response HTTP响应
             * @param file_info 文件信息
             * @param file_path 文件路径
             * @param head_only 是否只返回头部
             */
            void handleRangeRequest(const HttpRequest& request, HttpResponse& response,
                                  const FileInfo& file_info, const std::string& file_path, bool head_only = false);

            /**
             * @brief 解析Range头
             * @param range_header Range头内容
             * @param file_size 文件大小
             * @return 范围信息
             */
            RangeInfo parseRangeHeader(const std::string& range_header, size_t file_size);

            // ==================== 目录处理方法 ====================

            /**
             * @brief 处理目录请求
             * @param request HTTP请求
             * @param response HTTP响应
             * @param dir_path 目录路径
             * @param url_path URL路径
             */
            void handleDirectoryRequest(const HttpRequest& request, HttpResponse& response,
                                      const std::string& dir_path, const std::string& url_path);

            /**
             * @brief 检查目录是否为空
             * @param dir_path 目录路径
             * @return 目录是否为空
             */
            bool isDirectoryEmpty(const std::string& dir_path);

            /**
             * @brief 生成空目录列表页面
             * @param response HTTP响应
             * @param dir_path 目录路径
             * @param url_path URL路径
             */
            void generateEmptyDirectoryListing(HttpResponse& response, const std::string& dir_path, const std::string& url_path);

            /**
             * @brief 生成403禁止访问页面
             * @param url_path URL路径
             * @return HTML页面内容
             */
            std::string generateForbiddenPage(const std::string& url_path);

            /**
             * @brief 获取父级路径
             * @param url_path URL路径
             * @return 父级路径
             */
            std::string getParentPath(const std::string& url_path);

            /**
             * @brief 获取当前时间字符串
             * @return 当前时间的字符串表示
             */
            std::string getCurrentTimeString();

            /**
             * @brief 生成目录列表
             * @param response HTTP响应
             * @param dir_path 目录路径
             * @param url_path URL路径
             */
            void generateDirectoryListing(HttpResponse& response, const std::string& dir_path, const std::string& url_path);

            /**
             * @brief 生成目录统计信息
             * @param html HTML流
             * @param dir_path 目录路径
             */
            void generateDirectoryStats(std::ostringstream& html, const std::string& dir_path);

            /**
             * @brief 列出目录内容
             * @param html HTML流
             * @param dir_path 目录路径
             * @param url_path URL路径
             */
            void listDirectoryContents(std::ostringstream& html, const std::string& dir_path, const std::string& url_path);

            /**
             * @brief 格式化文件大小
             * @param size 文件大小
             * @return 格式化后的大小字符串
             */
            std::string formatFileSize(size_t size);

            /**
             * @brief 获取文件图标
             * @param filename 文件名
             * @return 文件图标emoji
             */
            std::string getFileIcon(const std::string& filename);

            /**
             * @brief 获取文件类型描述
             * @param filename 文件名
             * @return 文件类型描述
             */
            std::string getFileTypeDescription(const std::string& filename);
        };

    } // namespace http
} // namespace common

#endif // HTTP_SERVER_H
