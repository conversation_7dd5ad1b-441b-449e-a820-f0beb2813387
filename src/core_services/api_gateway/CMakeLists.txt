# src/core_services/api_gateway/CMakeLists.txt
cmake_minimum_required(VERSION 3.16)

# 设置API网关模块
set(API_GATEWAY_SOURCES
        api_gateway.cpp
        load_balancer.cpp
        rate_limiter.cpp
        circuit_breaker.cpp
        service_route.cpp
        service_discovery.cpp
        service_registry_handler.cpp
)

set(API_GATEWAY_HEADERS
        ${CMAKE_SOURCE_DIR}/include/core_services/api_gateway/api_gateway.h
        ${CMAKE_SOURCE_DIR}/include/core_services/api_gateway/load_balancer.h
        ${CMAKE_SOURCE_DIR}/include/core_services/api_gateway/rate_limiter.h
        ${CMAKE_SOURCE_DIR}/include/core_services/api_gateway/circuit_breaker.h
        ${CMAKE_SOURCE_DIR}/include/core_services/api_gateway/service_route.h
        ${CMAKE_SOURCE_DIR}/include/core_services/api_gateway/service_discovery.h
        ${CMAKE_SOURCE_DIR}/include/core_services/api_gateway/service_registry_handler.h
)

# 创建API网关静态库
add_library(api_gateway STATIC ${API_GATEWAY_SOURCES} ${API_GATEWAY_HEADERS})

# 设置包含目录
target_include_directories(api_gateway PUBLIC
        ${CMAKE_SOURCE_DIR}/include
        ${CMAKE_SOURCE_DIR}/third_party/nlohmann/include
)

# 添加依赖关系确保构建顺序
add_dependencies(api_gateway
        http_module
        network_lib
        thread_pool_lib
        common_logger_lib
        common_config_lib
)

# 链接依赖库
target_link_libraries(api_gateway
        http_module
        network_lib
        thread_pool_lib
        common_logger_lib
        common_config_lib
        pthread
)

# 设置编译选项
target_compile_features(api_gateway PUBLIC cxx_std_17)
target_compile_options(api_gateway PRIVATE
        -Wall -Wextra -Wpedantic
        $<$<CONFIG:Debug>:-g -O0>
        $<$<CONFIG:Release>:-O3 -DNDEBUG>
)

# 创建API网关可执行文件
add_executable(api_gateway_server main.cpp)

# 添加依赖关系确保构建顺序
add_dependencies(api_gateway_server api_gateway)

# 链接库
target_link_libraries(api_gateway_server api_gateway)

# 安装规则
install(TARGETS api_gateway api_gateway_server
        LIBRARY DESTINATION lib
        ARCHIVE DESTINATION lib
        RUNTIME DESTINATION bin
)

install(FILES ${API_GATEWAY_HEADERS}
        DESTINATION include/core_services/api_gateway
)