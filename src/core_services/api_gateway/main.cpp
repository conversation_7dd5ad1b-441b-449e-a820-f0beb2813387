//
// Created by 29108 on 2025/7/15.
//
#include "core_services/api_gateway/api_gateway.h"
#include "common/logger/logger.h"
#include "common/config/config_manager.h"

/**
 * @brief API网关基本使用示例
 * @details 演示如何创建、配置和启动API网关
 */
int main() {
    try {
        // 1. 初始化配置管理器
        auto& config_manager = common::config::ConfigManager::getInstance();
        config_manager.loadFromFile("../../../../config/api_gateway_config_example.yaml");

        // 2. 初始化日志系统
        common::logger::Logger::getInstance().initializeFromConfig();

        // 3. 创建API网关配置
        auto gateway_config = core_services::api_gateway::ApiGateway::Config::fromConfigManager();

        // 4. 创建API网关实例
        core_services::api_gateway::ApiGateway gateway(gateway_config);

        // 5. 添加认证服务路由
        LOG_DEBUG("开始配置认证服务路由...");

        // 认证API路由
        core_services::api_gateway::ServiceRoute auth_service_route;
        auth_service_route.service_name = "auth_service";
        auth_service_route.path_pattern = "/api/v1/auth/*";
        auth_service_route.methods = {"GET", "POST", "PUT", "DELETE", "OPTIONS"};
        auth_service_route.target_host = "localhost";
        auth_service_route.target_port = 8008;
        auth_service_route.target_path_prefix = "";  // 不去除前缀，直接转发
        auth_service_route.strip_path_prefix = false;  // 保持原始路径
        auth_service_route.timeout_ms = 30000;
        auth_service_route.retry_count = 3;
        auth_service_route.enable_circuit_breaker = true;
        auth_service_route.enable_rate_limiting = true;
        auth_service_route.match_type = core_services::api_gateway::ServiceRoute::MatchType::PREFIX;
        auth_service_route.enabled = true;
        auth_service_route.route_id = "auth_service_route";  // 添加路由ID

        LOG_DEBUG("认证服务路由配置: path=" + auth_service_route.path_pattern +
                 ", service=" + auth_service_route.service_name +
                 ", target=" + auth_service_route.target_host + ":" + std::to_string(auth_service_route.target_port));

        gateway.addServiceRoute(auth_service_route);
        LOG_INFO("认证服务路由已添加: " + auth_service_route.path_pattern);

        // 游戏服务路由（指向认证服务的游戏相关端点）
        // core_services::api_gateway::ServiceRoute game_auth_route;
        // game_auth_route.service_name = "auth_service";  // 游戏相关的认证也由认证服务处理
        // game_auth_route.path_pattern = "/api/v1/game/*";
        // game_auth_route.methods = {"GET", "POST", "PUT", "DELETE", "OPTIONS"};
        // game_auth_route.target_host = "localhost";
        // game_auth_route.target_port = 8008;  // 认证服务端口
        // game_auth_route.target_path_prefix = "";  // 不去除前缀，直接转发
        // game_auth_route.strip_path_prefix = false;  // 保持原始路径
        // game_auth_route.timeout_ms = 30000;
        // game_auth_route.retry_count = 3;
        // game_auth_route.enable_circuit_breaker = true;
        // game_auth_route.enable_rate_limiting = true;
        // game_auth_route.match_type = core_services::api_gateway::ServiceRoute::MatchType::PREFIX;
        // game_auth_route.enabled = true;
        // game_auth_route.route_id = "game_auth_route";  // 添加路由ID
        //
        // LOG_DEBUG("游戏认证路由配置: path=" + game_auth_route.path_pattern +
        //          ", service=" + game_auth_route.service_name +
        //          ", target=" + game_auth_route.target_host + ":" + std::to_string(game_auth_route.target_port));
        //
        // gateway.addServiceRoute(game_auth_route);
        // LOG_INFO("游戏认证路由已添加: " + game_auth_route.path_pattern);

        LOG_INFO("认证服务路由配置完成");

        // 验证路由配置
        auto all_routes = gateway.getAllRoutes();
        LOG_INFO("当前已配置的路由总数: " + std::to_string(all_routes.size()));
        for (const auto& route : all_routes) {
            LOG_INFO("路由: " + route.service_name + " " + route.path_pattern + " -> " +
                     route.target_host + ":" + std::to_string(route.target_port));
        }

        // 测试路由匹配
        LOG_INFO("测试路由匹配:");
        auto test_route = gateway.findRoute("POST", "/api/v1/auth/login");
        if (test_route) {
            LOG_INFO("✓ POST /api/v1/auth/login 匹配到路由: " + test_route->service_name);
        } else {
            LOG_ERROR("✗ POST /api/v1/auth/login 未找到匹配路由");
        }

        // 测试其他路由
        auto test_route2 = gateway.findRoute("POST", "/api/v1/auth/register");
        if (test_route2) {
            LOG_INFO("✓ POST /api/v1/auth/register 匹配到路由: " + test_route2->service_name);
        } else {
            LOG_ERROR("✗ POST /api/v1/auth/register 未找到匹配路由");
        }

        // 6. 等待认证服务自动注册
        LOG_DEBUG("等待认证服务自动注册...");
        LOG_DEBUG("认证服务将在启动后自动注册到API网关");
        LOG_DEBUG("支持的端点: /api/v1/auth/*, /api/v1/game/*");

        // 可选：注册备用认证服务实例（如果有多个实例）
        // core_services::api_gateway::LoadBalancer::ServiceInstance auth_instance2;
        // auth_instance2.host = "localhost";
        // auth_instance2.port = 8009;
        // auth_instance2.weight = 1;
        // auth_instance2.healthy = true;
        // gateway.registerServiceInstance("auth_service", auth_instance2);

        // 7. 验证配置完整性
        LOG_DEBUG("验证系统配置...");

        // 显示关键路由信息
        LOG_DEBUG("关键认证路由配置:");
        LOG_DEBUG("  - POST /api/v1/auth/register -> localhost:8008");
        LOG_DEBUG("  - POST /api/v1/auth/login -> localhost:8008");
        LOG_DEBUG("  - POST /api/v1/game/login -> localhost:8008");
        LOG_DEBUG("  - GET /api/v1/game/servers -> localhost:8008");

        // 8. 启动网关
        LOG_INFO("启动API网关...");
        gateway.start();

        LOG_INFO("API网关启动完成，监听端口: 8080");
        LOG_DEBUG("健康检查: http://localhost:8080/health");
        LOG_DEBUG("服务统计: http://localhost:8080/api/v1/services/stats");
        LOG_DEBUG("认证服务路由测试:");
        LOG_DEBUG("  curl -X POST http://localhost:8080/api/v1/auth/register -H 'Content-Type: application/json' -d '{\"username\":\"test\",\"password\":\"test123\",\"email\":\"<EMAIL>\"}'");

        LOG_INFO("API网关启动成功，按任意键停止...");
        std::cin.get();

        // 8. 停止网关
        gateway.stop();

    } catch (const std::exception& e) {
        LOG_ERROR("API网关运行失败: " + std::string(e.what()));
        return 1;
    }

    return 0;
}