# API网关完整配置示例
# 支持YAML和JSON格式

# API网关配置
api_gateway:
  # HTTP服务器配置
  server:
    host: "0.0.0.0"                    # 监听地址
    port: 8080                         # 监听端口
    worker_threads: 4                  # 工作线程数
    keep_alive_timeout_ms: 60000       # Keep-Alive超时时间（毫秒）
    request_timeout_ms: 30000          # 请求超时时间（毫秒）
    max_request_size: 1048576          # 最大请求大小（字节，1MB）
    max_header_size: 8192              # 最大请求头大小（字节，8KB）

  # 负载均衡配置
  load_balancer:
    strategy: "round_robin"            # 负载均衡策略：round_robin, weighted_round_robin, least_connections

  # 认证配置
  auth:
    enable: true                       # 是否启用认证
    jwt_secret: "your_jwt_secret_key_here"  # JWT密钥
    jwt_expiry_seconds: 3600           # JWT过期时间（秒）
    public_paths: "/health,/metrics,/api/v1/auth/login,/api/v1/auth/register,/api/v1/game/servers,/api/v1/services"  # 公开路径（无需认证）

  # 监控配置
  monitoring:
    enable_metrics: true               # 启用指标收集
    enable_tracing: true               # 启用链路追踪
    metrics_path: "/metrics"           # 指标暴露路径

  # CORS配置
  cors:
    enable: true                       # 启用CORS
    allowed_origins: "*"               # 允许的来源，多个用逗号分隔
    allowed_methods: "GET,POST,PUT,DELETE,OPTIONS,HEAD,PATCH"  # 允许的HTTP方法
    allowed_headers: "Content-Type,Authorization,X-Requested-With,Accept,Origin,X-API-Key"  # 允许的请求头

# 限流配置
rate_limiter:
  requests_per_second: 100             # 每秒请求数限制
  burst_size: 200                      # 突发请求大小
  window_size: 60                      # 时间窗口大小（秒）
  enable_path_based_limiting: true     # 启用基于路径的限流
  enable_client_based_limiting: true   # 启用基于客户端的限流
  refill_interval_ms: 100              # 令牌补充间隔（毫秒）

# 熔断器配置
circuit_breaker:
  failure_threshold: 5                 # 失败阈值
  recovery_timeout_ms: 30000           # 恢复超时时间（毫秒）
  success_threshold: 3                 # 成功阈值
  timeout_ms: 5000                     # 请求超时时间（毫秒）

# 服务发现配置
service_discovery:
  enable: true                         # 启用服务发现
  health_check_interval: 30            # 健康检查间隔（秒）
  service_timeout: 60                  # 服务超时时间（秒）
  health_check_timeout_ms: 5000        # 健康检查超时时间（毫秒）
  max_retry_count: 3                   # 最大重试次数
  auto_deregister_unhealthy: true
  provider: "consul"
  consul:
    host: "consul.internal"
    port: 8500
    health_check_interval: 10
  refresh_interval: 30      # 自动注销不健康服务

# 路由配置
routes:
  # 认证服务路由配置
  auth_service_route:
    path: "/api/v1/auth/*"
    service: "auth-service"
    target_host: "localhost"
    target_port: 8008
    target_path_prefix: "/api/v1/auth"
    strip_path_prefix: false
    methods: "GET,POST,PUT,DELETE,OPTIONS"
    timeout: 30
    retry_count: 3
    enable_circuit_breaker: true
    enable_rate_limiting: true
    match_type: "prefix"
    priority: 120
    enabled: true
    route_id: "auth-service-api"
    health_check_enabled: true
    health_check_path: "/health"
    health_check_interval: 30
    load_balancer_strategy: "round_robin"
    headers_to_add:
      X-Service-Name: "auth-service"
      X-Gateway-Version: "1.0.0"
    headers_to_remove: "X-Internal-Token,X-Debug-Info"

  # 游戏相关认证路由
  game_auth_route:
    path: "/api/v1/game/*"
    service: "auth-service"
    target_host: "localhost"
    target_port: 8008
    target_path_prefix: "/api/v1/game"
    strip_path_prefix: false
    methods: "GET,POST,PUT,DELETE,OPTIONS"
    timeout: 30
    retry_count: 3
    enable_circuit_breaker: true
    enable_rate_limiting: true
    match_type: "prefix"
    priority: 115
    enabled: true
    route_id: "game-auth-api"
    health_check_enabled: true
    health_check_path: "/health"
    health_check_interval: 30
    load_balancer_strategy: "round_robin"
    headers_to_add:
      X-Service-Name: "auth-service"
      X-Gateway-Version: "1.0.0"

  # 用户服务路由（保留作为参考，已禁用）
  user_service_route:
    path: "/api/users/*"
    service: "user-service"
    target_host: "user-service.internal"
    target_port: 8080
    target_path_prefix: "/api/v1"
    strip_path_prefix: true
    methods: "GET,POST,PUT,DELETE"
    timeout: 30
    retry_count: 3
    enable_circuit_breaker: true
    enable_rate_limiting: true
    match_type: "prefix"
    priority: 100
    enabled: false
    route_id: "user-service-api"
    health_check_enabled: true
    health_check_path: "/health"
    health_check_interval: 30
    load_balancer_strategy: "round_robin"
    headers_to_add:
      X-Service-Name: "user-service"
      X-Gateway-Version: "1.0.0"
    headers_to_remove: "X-Internal-Token,X-Debug-Info"

  # 版本化API路由 - 用户服务
  user_service_v1_route:
    path: "/api/v1/users/*"
    service: "user-service"
    target_host: "user-service.internal"
    target_port: 8080
    target_path_prefix: "/api/v1"
    strip_path_prefix: false
    methods: "GET,POST,PUT,DELETE"
    timeout: 30
    retry_count: 3
    enable_circuit_breaker: true
    enable_rate_limiting: true
    match_type: "prefix"
    priority: 110
    enabled: true
    route_id: "user-service-v1-api"
    health_check_enabled: true
    health_check_path: "/health"
    health_check_interval: 30
    load_balancer_strategy: "round_robin"

  # 通用版本化API路由
  generic_v1_route:
    path: "/api/v1/*"
    service: "default-service"
    target_host: "localhost"
    target_port: 8080
    target_path_prefix: "/api/v1"
    strip_path_prefix: false
    methods: "GET,POST,PUT,DELETE,PATCH,OPTIONS"
    timeout: 30
    retry_count: 3
    enable_circuit_breaker: true
    enable_rate_limiting: true
    match_type: "prefix"
    priority: 50
    enabled: true
    route_id: "generic-v1-api"

  # 订单服务路由
  order_service_route:
    path: "/api/orders/*"
    service: "order-service"
    target_host: "order-service.internal"
    target_port: 8081
    target_path_prefix: "/api/v1"
    strip_path_prefix: true
    methods: "GET,POST,PUT,DELETE,PATCH"
    timeout: 45
    retry_count: 2
    enable_circuit_breaker: true
    enable_rate_limiting: false
    match_type: "prefix"
    priority: 90
    enabled: true
    route_id: "order-service-api"
    health_check_enabled: true
    health_check_path: "/actuator/health"
    health_check_interval: 60
    load_balancer_strategy: "weighted_round_robin"

  # 支付服务路由
  payment_service_route:
    path: "/api/payments/*"
    service: "payment-service"
    target_host: "payment-service.internal"
    target_port: 8082
    target_path_prefix: "/api/v1"
    strip_path_prefix: false
    methods: "POST,GET"
    timeout: 60
    retry_count: 1
    enable_circuit_breaker: true
    enable_rate_limiting: true
    match_type: "prefix"
    priority: 200
    enabled: true
    route_id: "payment-service-api"
    health_check_enabled: true
    health_check_path: "/health"
    health_check_interval: 15
    load_balancer_strategy: "least_connections"


  # 静态文件服务路由
  static_files_route:
    path: "/static/*"
    service: "static-file-service"
    target_host: "nginx.internal"
    target_port: 80
    target_path_prefix: "/static"
    strip_path_prefix: false
    methods: "GET,HEAD"
    timeout: 10
    retry_count: 0
    enable_circuit_breaker: false
    enable_rate_limiting: false
    match_type: "prefix"
    priority: 10
    enabled: true
    route_id: "static-files"
    health_check_enabled: false
    load_balancer_strategy: "round_robin"

  # 健康检查路由
  health_check_route:
    path: "/health"
    service: "health-service"
    target_host: "localhost"
    target_port: 8090
    target_path_prefix: ""
    strip_path_prefix: false
    methods: "GET"
    timeout: 5
    retry_count: 0
    enable_circuit_breaker: false
    enable_rate_limiting: false
    match_type: "exact"
    priority: 1000
    enabled: true
    route_id: "health-check"
    health_check_enabled: false
    load_balancer_strategy: "round_robin"
  hot_reload:
    enabled: true
    check_interval: 30

# 热重载配置

# 日志配置
logging:
  level: "INFO"                        # 日志级别：DEBUG, INFO, WARN, ERROR
  enable_access_log: true              # 启用访问日志
  access_log_format: "combined"        # 访问日志格式
  enable_error_log: true               # 启用错误日志
  log_file_path: "/var/log/api_gateway.log"  # 日志文件路径

# 安全配置
security:
  enable_rate_limiting: true           # 启用全局限流
  enable_ddos_protection: true         # 启用DDoS防护
  max_request_per_ip: 1000             # 每个IP最大请求数
  block_suspicious_requests: true      # 阻止可疑请求
  enable_request_validation: true      # 启用请求验证

# 缓存配置
cache:
  enable_response_cache: true          # 启用响应缓存
  cache_ttl_seconds: 300               # 缓存TTL（秒）
  cache_size_mb: 100                   # 缓存大小（MB）
  cache_key_prefix: "api_gateway:"     # 缓存键前缀


