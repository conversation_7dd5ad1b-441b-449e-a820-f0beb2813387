# 认证服务配置文件
# Game Microservices Auth Service Configuration
# 版本: 1.0.0
# 作者: 29108
# 日期: 2025/7/21
#
# 基于FINAL_ARCHITECTURE_DECISION.md和KAFKA_INTEGRATION_ANALYSIS.md的架构设计
# 与API Gateway协作，专注于认证业务逻辑，支持事件驱动的实时状态更新

# ==================== 服务基础配置 ====================
service:
  name: "auth_service"
  version: "1.0.0"
  host: "0.0.0.0"                  # 监听所有接口
  port: 8008
  worker_threads: 8
  enable_https: false
  ssl_cert_file: ""
  ssl_key_file: ""

# ==================== 数据库配置 ====================
database:
  mysql:
    host: "dev-mysql"                # 改为localhost，更可靠
    port: 3306
    database: "game_microservices"
    username: "root"
    password: "123456"
    pool_size: 20                    # 增加连接池大小
    max_pool_size: 100               # 增加最大连接数
    connection_timeout: 15           # 增加连接超时时间
    query_timeout: 30                # 增加查询超时时间
    enable_ssl: false
    charset: "utf8mb4"
    idle_timeout: 600                # 10分钟空闲超时

  redis:
    host: "redis"
    port: 6379
    password: "123456"
    database: 0
    pool_size: 20                    # 增加初始连接数
    max_pool_size: 50                # 增加最大连接数
    connection_timeout: 10           # 增加连接超时
    read_timeout: 5
    write_timeout: 5
    idle_timeout: 300                # 5分钟空闲超时

# ==================== Kafka配置 ====================
kafka:
  enable: true
  brokers: "kafka:9092"
  topic_prefix: "game_microservices"
  producer:
    acks: "all"
    retries: 3
    batch_size: 16384
    linger_ms: 1
    buffer_memory: 33554432
  consumer:
    group_id: "auth_service_group"
    auto_offset_reset: "latest"
    enable_auto_commit: true
    auto_commit_interval_ms: 1000

# ==================== 服务发现配置 ====================
service_discovery:
  enable: true
  api_gateway:
    host: "localhost"
    port: 8080
    register_endpoint: "/api/v1/services/register"
    heartbeat_endpoint: "/api/v1/services/heartbeat"
  heartbeat_interval: 30
  health_check_endpoint: "/health"

# ==================== JWT配置 ====================
jwt:
  secret_key: "game_microservices_jwt_secret_key_2025_very_secure"
  issuer: "game-microservices-auth"
  audience: "game-microservices-clients"
  access_token_expire_seconds: 3600      # 1小时
  refresh_token_expire_seconds: 604800   # 7天
  algorithm: "HS256"
  enable_token_blacklist: true
  blacklist_cleanup_interval_hours: 24
  enable_jti: true
  strict_audience_check: true
  strict_issuer_check: true
# ==================== 密码策略配置 ====================
password:
  min_length: 8
  max_length: 128
  require_uppercase: true
  require_lowercase: true
  require_digits: true
  require_special_chars: true
  min_special_chars: 1
  min_digits: 1
  min_uppercase: 1
  min_lowercase: 1
  forbid_common_passwords: true
  forbid_personal_info: true
  bcrypt_cost: 12
  special_chars: "!@#$%^&*()_+-=[]{}|;:,.<>?"

# ==================== 会话管理配置 ====================
session:
  session_timeout: 3600                # 1小时 (3600秒)
  max_idle_time: 1800                  # 30分钟 (1800秒)
  max_concurrent_sessions: 10
  enable_multi_device: true
  enable_ip_validation: true
  enable_device_validation: false
  enable_session_migration: true
  cleanup_interval: 60                 # 1分钟 (更频繁的清理)
  enable_session_encryption: true
  encryption_key: "session_encryption_key_2025_very_secure"
  enable_anomaly_detection: true
  max_ip_change_frequency: 0.1          # 每小时最大IP变化频率

# ==================== 用户仓库配置 ====================
user_repository:
  enable_cache: true
  default_cache_ttl: 3600               # 1小时
  user_cache_ttl: 1800                  # 30分钟
  game_data_cache_ttl: 600              # 10分钟
  enable_read_write_split: false
  max_batch_size: 1000
  enable_async_operations: true
  connection_timeout_seconds: 30
  query_timeout_seconds: 60

# ==================== 性能配置 ====================
performance:
  max_concurrent_requests: 1000
  request_timeout: 30
  enable_request_logging: true
  enable_metrics: true
  thread_pool_size: 16
  scheduler_thread_pool_size: 4

# ==================== 日志配置 ====================
logging:
  level: "DEBUG"                         # DEBUG, INFO, WARNING, ERROR
  file: "logs/auth_service.log"
  console: true
  max_file_size: "100MB"
  max_files: 10
  enable_json_format: false
  enable_async_logging: true

# ==================== 监控配置 ====================
monitoring:
  enable_health_check: true
  health_check_interval: 30
  enable_metrics_collection: true
  metrics_endpoint: "/metrics"
  enable_prometheus: false
  prometheus_port: 9090

# ==================== 安全配置 ====================
security:
  enable_rate_limiting: true
  rate_limit_requests_per_minute: 100
  enable_cors: true
  cors_allowed_origins: ["*"]
  cors_allowed_methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"]
  cors_allowed_headers: ["Content-Type", "Authorization"]
  enable_request_validation: true
  max_request_size: "10MB"
# ==================== 游戏配置 ====================
game:
  supported_types:
    - "snake"
    - "tetris"
    - "chess"
    - "poker"
    - "rpg"
    - "moba"
    - "fps"
    - "strategy"

  server_selection:
    algorithm: "least_connections"      # round_robin, least_connections, weighted_round_robin
    health_check_interval: 60
    max_players_per_server: 100
    enable_region_preference: true

  session:
    game_session_timeout: 7200          # 2小时
    enable_game_session_migration: true
    max_game_sessions_per_user: 3

# ==================== 邮件配置 ====================
email:
  enable: false
  smtp_host: "smtp.gmail.com"
  smtp_port: 587
  smtp_user: ""
  smtp_password: ""
  from_address: "<EMAIL>"
  from_name: "Game Microservices"

# ==================== 第三方登录配置 ====================
oauth:
  enable_google: false
  google_client_id: ""
  google_client_secret: ""

  enable_github: false
  github_client_id: ""
  github_client_secret: ""

  enable_wechat: false
  wechat_app_id: ""
  wechat_app_secret: ""

  # 游戏平台登录
  enable_steam: false
  steam_app_id: ""
  steam_api_key: ""

  enable_qq: false
  qq_app_id: ""
  qq_app_key: ""

# ==================== 开发配置 ====================
development:
  enable_debug_mode: false
  enable_test_endpoints: false
  mock_external_services: false
  enable_detailed_error_messages: false
  enable_request_tracing: false

# ==================== 环境特定配置 ====================
environment: "development"              # development, testing, staging, production

# 生产环境覆盖配置
production:
  logging:
    level: "WARNING"
    console: false
  security:
    enable_cors: false
    cors_allowed_origins: ["https://yourgame.com"]
  development:
    enable_debug_mode: false
    enable_test_endpoints: false
    enable_detailed_error_messages: false

# 测试环境覆盖配置
testing:
  database:
    mysql:
      database: "game_microservices_test"
    redis:
      database: 1
  jwt:
    access_token_expire_seconds: 300    # 5分钟（测试用）
  development:
    enable_debug_mode: true
    enable_test_endpoints: true
    mock_external_services: true
