#include <iostream>
#include <vector>
#include <string>

// 模拟ServiceRoute的关键部分
struct ServiceRoute {
    std::string service_name;
    std::string path_pattern;
    std::vector<std::string> methods;
    
    enum class MatchType {
        EXACT,
        PREFIX,
        REGEX
    };
    MatchType match_type = MatchType::PREFIX;
    
    bool matchPath(const std::string& path) const {
        switch (match_type) {
            case MatchType::PREFIX:
                if (path_pattern.empty()) return false;
                
                // 处理通配符前缀匹配（如 /api/users/*）
                if (path_pattern.back() == '*') {
                    std::string prefix = path_pattern.substr(0, path_pattern.length() - 1);
                    return path.find(prefix) == 0;
                }
                
                // 普通前缀匹配
                return path.find(path_pattern) == 0;
                
            default:
                return path_pattern == path;
        }
    }
    
    bool supportMethod(const std::string& method) const {
        if (methods.empty()) return true;
        for (const auto& m : methods) {
            if (m == method) return true;
        }
        return false;
    }
};

int main() {
    // 模拟API Gateway中的路由配置
    ServiceRoute auth_route;
    auth_route.service_name = "auth_service";
    auth_route.path_pattern = "/api/v1/auth/*";
    auth_route.methods = {"GET", "POST", "PUT", "DELETE", "OPTIONS"};
    auth_route.match_type = ServiceRoute::MatchType::PREFIX;
    
    // 测试路径
    std::string test_path = "/api/v1/auth/login";
    std::string test_method = "POST";
    
    std::cout << "路由配置:" << std::endl;
    std::cout << "  service_name: " << auth_route.service_name << std::endl;
    std::cout << "  path_pattern: " << auth_route.path_pattern << std::endl;
    std::cout << "  methods: ";
    for (const auto& method : auth_route.methods) {
        std::cout << method << " ";
    }
    std::cout << std::endl;
    
    std::cout << "\n测试请求:" << std::endl;
    std::cout << "  method: " << test_method << std::endl;
    std::cout << "  path: " << test_path << std::endl;
    
    std::cout << "\n匹配结果:" << std::endl;
    std::cout << "  路径匹配: " << (auth_route.matchPath(test_path) ? "是" : "否") << std::endl;
    std::cout << "  方法匹配: " << (auth_route.supportMethod(test_method) ? "是" : "否") << std::endl;
    
    // 详细的路径匹配分析
    std::cout << "\n路径匹配分析:" << std::endl;
    std::string prefix = auth_route.path_pattern.substr(0, auth_route.path_pattern.length() - 1);
    std::cout << "  去除*后的前缀: '" << prefix << "'" << std::endl;
    std::cout << "  测试路径是否以前缀开始: " << (test_path.find(prefix) == 0 ? "是" : "否") << std::endl;
    
    return 0;
}
