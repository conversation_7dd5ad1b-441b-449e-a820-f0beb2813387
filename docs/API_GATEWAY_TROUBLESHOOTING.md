# API Gateway 路由问题排查和解决方案

## 问题现象
- 前端请求 `POST http://localhost:8080/api/v1/auth/login` 返回 "Route not found"
- 日志显示：`未找到匹配的路由: POST /api/v1/auth/login`
- 服务实例被清理：`清理过期服务实例: auth_service`

## 问题分析

### 1. 双重路由系统
API Gateway使用了两层路由系统：
- **HTTP路由层**：`http_server_->post("/api/v1/auth/login", api_handler)`
- **服务路由层**：`route_manager_->findRoute("POST", "/api/v1/auth/login")`

### 2. 服务生命周期问题
- Auth Service注册后，由于心跳或健康检查失败被清理
- 默认服务超时时间60秒可能过短

### 3. 路由配置时机问题
- 静态路由配置在main.cpp中
- 动态服务注册通过HTTP API

## 解决方案

### 1. 增加服务超时时间
已修改：`service_timeout` 从60秒增加到120秒

### 2. 添加详细调试日志
已添加：
- 路由添加时的详细日志
- 路由查找时的详细日志
- 服务注册验证日志

### 3. 路由配置验证
已添加：
- 启动时验证所有路由配置
- 测试关键路由的匹配功能

### 4. 心跳机制优化
建议：
- 减少心跳间隔（从30秒改为15秒）
- 增加健康检查重试次数
- 添加心跳失败的详细日志

## 验证步骤

### 1. 检查路由配置
```bash
# 查看API Gateway启动日志，确认路由配置
grep "路由已添加\|匹配到路由" api_gateway.log
```

### 2. 检查服务注册
```bash
# 查看服务注册日志
curl http://localhost:8080/api/v1/services/stats
```

### 3. 测试路由匹配
```bash
# 直接测试登录接口
curl -v -X POST http://localhost:8080/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username":"test","password":"test123"}'
```

### 4. 检查服务健康状态
```bash
# 检查Auth Service是否正常运行
curl http://localhost:8008/health
```

## 预期修复效果

1. **路由匹配成功**：POST /api/v1/auth/login 能够找到匹配的路由
2. **服务实例稳定**：Auth Service实例不会被过早清理
3. **请求转发正常**：API Gateway能够正确转发请求到Auth Service
4. **详细日志输出**：提供足够的调试信息用于问题排查

## 后续优化建议

1. **配置文件化**：将路由配置移到配置文件中
2. **动态路由**：支持通过API动态添加/删除路由
3. **健康检查优化**：实现更智能的健康检查机制
4. **监控告警**：添加服务状态监控和告警机制
